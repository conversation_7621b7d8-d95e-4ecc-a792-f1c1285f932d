/* AdminOrderManagement Component Styles */
.AdminOrderManagement {
  padding: var(--basefont);
  background-color: var(--bg-gray);
  min-height: 100vh;
}

/* Header Section */
.AdminOrderManagement__header {
  margin-bottom: var(--heading6);
}

.AdminOrderManagement .header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--basefont);
}

.AdminOrderManagement .header-text h1 {
  color: var(--primary-color);
  font-size: var(--heading4);
  font-weight: 700;
  margin: 0 0 var(--smallfont) 0;
}

.AdminOrderManagement .header-text p {
  color: var(--dark-gray);
  font-size: var(--basefont);
  margin: 0;
}

.AdminOrderManagement .header-actions {
  display: flex;
  gap: var(--basefont);
}

.AdminOrderManagement .header-actions .btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  transition: all 0.3s ease;
}

/* Filters Section */
.AdminOrderManagement__filters {
 
 gap: var(--basefont);
 flex-wrap: wrap;
  margin-bottom: var(--heading6);

  display: flex
;
    justify-content: space-between;
    align-items: flex-start;
}

.AdminOrderManagement .search-section {
  margin-bottom: var(--basefont);
  width: 100%;
  max-width: 500px;
}

.AdminOrderManagement .search-input {
  position: relative;
  max-width: 500px;
}

.AdminOrderManagement .search-input .search-icon {
  position: absolute;
  left: var(--basefont);
  top: 50%;
  transform: translateY(-50%);
  color: var(--dark-gray);
  font-size: var(--basefont);
}

.AdminOrderManagement .search-input input {
  width: 100%;
  padding: var(--basefont) var(--basefont) var(--basefont) 40px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  transition: border-color 0.3s ease;
}

.AdminOrderManagement .search-input input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.AdminOrderManagement .filter-section {
  display: flex;
  gap: var(--heading6);
  flex-wrap: wrap;
}

.AdminOrderManagement .filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.AdminOrderManagement .filter-group label {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--dark-gray);
}

.AdminOrderManagement .filter-group select,
.AdminOrderManagement .filter-group input[type="date"] {
  padding: var(--smallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  background-color: var(--white);
  min-width: 150px;
}

.AdminOrderManagement .filter-group input[type="date"]:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Table Section */
.AdminOrderManagement__table {
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Order-specific table styles */
.AdminOrderManagement .order-number {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  font-weight: 600;
  color: var(--text-color);
}

.AdminOrderManagement .order-icon {
  color: var(--text-color);
  font-size: var(--basefont);
}

.AdminOrderManagement .user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.AdminOrderManagement .user-name {
  font-weight: 600;
  color: var(--text-color);
  font-size: var(--smallfont);
}

.AdminOrderManagement .user-email {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.AdminOrderManagement .content-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex-direction: start;
}

.AdminOrderManagement .content-title {
  font-weight: 600;
  color: var(--text-color);
  font-size: var(--smallfont);
}

.AdminOrderManagement .content-type {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.AdminOrderManagement .order-type {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: uppercase;
}

.AdminOrderManagement .order-type-fixed {
  background-color: #e3f2fd;
  color: #1976d2;
}

.AdminOrderManagement .order-type-auction {
  background-color: #fff3e0;
  color: #f57c00;
}

.AdminOrderManagement .order-type-custom {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.AdminOrderManagement .amount-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.AdminOrderManagement .total-amount {
  font-weight: 600;
  color: var(--text-color);
  font-size: var(--smallfont);
}

.AdminOrderManagement .platform-fee {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.AdminOrderManagement .status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: uppercase;
}

.AdminOrderManagement .status-completed {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.AdminOrderManagement .status-processing {
  background-color: #fff3e0;
  color: #f57c00;
}

.AdminOrderManagement .status-pending {
  background-color: #fff8e1;
  color: #f9a825;
}

.AdminOrderManagement .status-cancelled {
  background-color: #ffebee;
  color: #d32f2f;
}

.AdminOrderManagement .status-refunded {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.AdminOrderManagement .status-default {
  background-color: var(--light-gray);
  color: var(--dark-gray);
}

.AdminOrderManagement .date-info {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.AdminOrderManagement .date-icon {
  color: var(--primary-color);
  font-size: var(--smallfont);
}

.AdminOrderManagement .action-buttons {
  display: flex;
  gap: var(--smallfont);
  justify-content: center;
}

.AdminOrderManagement .btn-icon {
  padding: 6px;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--smallfont);
}

.AdminOrderManagement .btn-view {
  background-color: transparent;
  color: var(--black);
}

.AdminOrderManagement .btn-view:hover {
  color: var(--btn-color);
}

.AdminOrderManagement .btn-edit {
  background-color: #fff3e0;
  color: #f57c00;
}

.AdminOrderManagement .btn-edit:hover {
  background-color: #ffe0b2;
}

.AdminOrderManagement .btn-delete {
  background-color: #ffebee;
  color: #d32f2f;
}

.AdminOrderManagement .btn-delete:hover {
  background-color: #ffcdd2;
}

/* Modal Styles */
.AdminOrderManagement .modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.AdminOrderManagement .modal-content {
  background: var(--white);
  border-radius: var(--border-radius);
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.AdminOrderManagement .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--heading6);
  border-bottom: 1px solid var(--light-gray);
}

.AdminOrderManagement .modal-header h3 {
  margin: 0;
  color: var(--txt-color);
  font-size: var(--heading5);
}

.AdminOrderManagement .modal-close {
  background: none;
  border: none;
  font-size: var(--heading6);
  color: var(--dark-gray);
  cursor: pointer;
  padding: var(--smallfont);
  border-radius: var(--border-radius);
  transition: background-color 0.3s ease;
}

.AdminOrderManagement .modal-close:hover {
  background-color: var(--light-gray);
}

.AdminOrderManagement .modal-body {
  padding: var(--heading6);
}

.AdminOrderManagement .order-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--heading6);
}

.AdminOrderManagement .detail-section {
  background-color: var(--bg-gray);
  padding: var(--basefont);
  border-radius: var(--border-radius);
}

.AdminOrderManagement .detail-section h4 {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin: 0 0 var(--basefont) 0;
  color: var(--text-color);
  font-size: var(--basefont);
}

.AdminOrderManagement .detail-section p {
  margin: var(--smallfont) 0 !important;
  font-size: var(--smallfont);
}

.AdminOrderManagement .detail-section strong {
  color: var(--text-color);
}

.AdminOrderManagement .modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--basefont);
  padding: var(--heading6);
  border-top: 1px solid var(--light-gray);
}

/* Responsive Design */

/* Tablet Responsive */
@media (max-width: 1024px) and (min-width: 769px) {
  .AdminOrderManagement {
    padding: var(--basefont);
  }

  .AdminOrderManagement .filter-section {
    gap: var(--basefont);
  }

  .AdminOrderManagement .filter-group select,
  .AdminOrderManagement .filter-group input[type="date"] {
    min-width: 120px;
  }

  .AdminOrderManagement__table {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .AdminOrderManagement .modal-content {
    width: 85%;
  }

  .AdminOrderManagement .order-detail-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .AdminOrderManagement {
    padding: 0;
  }

  .AdminOrderManagement .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .AdminOrderManagement .filter-section {
    flex-direction: column;
    gap: var(--basefont);
  }

  .AdminOrderManagement .filter-group {
    width: 100%;
  }

  .AdminOrderManagement .filter-group select,
  .AdminOrderManagement .filter-group input[type="date"] {
    width: 100%;
    min-width: unset;
  }

  .AdminOrderManagement__table {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  .AdminOrderManagement .order-detail-grid {
    grid-template-columns: 1fr;
  }

  .AdminOrderManagement .modal-content {
    width: 95%;
    margin: var(--basefont);
    max-height: 85vh;
  }

  .AdminOrderManagement .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .AdminOrderManagement .btn-icon {
    padding: 8px;
    font-size: var(--basefont);
  }
  .AdminOrderManagement .search-input .search-icon {
    left: 5%;
    top: 50%;
  }
}

/* Small Mobile Responsive */
@media (max-width: 480px) {
  .AdminOrderManagement__header,
  .AdminOrderManagement__filters,
  .AdminOrderManagement__table {
    margin-bottom: var(--basefont);
  }

  .AdminOrderManagement .header-actions {
    width: 100%;
   
  }

  .AdminOrderManagement .header-actions .btn {
    flex: 1;
    justify-content: center;
    padding: var(--basefont);
    font-size: var(--basefont);
  }

  .AdminOrderManagement .search-input {
    max-width: 100%;
  }

  .AdminOrderManagement .modal-content {
    width: 98%;
    margin: var(--smallfont);
    max-height: 90vh;
  }

  .AdminOrderManagement .modal-header h3 {
    font-size: var(--basefont);
  }

  .AdminOrderManagement .detail-section {
    padding: var(--smallfont);
  }

  .AdminOrderManagement .detail-section h4 {
    font-size: var(--smallfont);
  }

  .AdminOrderManagement .detail-section p {
    font-size: var(--extrasmallfont);
  }

  /* Improve touch targets */
  .AdminOrderManagement .btn-icon {
    min-width: 44px;
    min-height: 44px;
    padding: 10px;
  }

  .AdminOrderManagement .modal-close {
    min-width: 44px;
    min-height: 44px;
    padding: var(--basefont);
  }

  /* Pagination responsive styles are now handled by AdminCMSPages.css */
}

/* Pagination styles are now handled by AdminCMSPages.css for consistency */
